<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增业务单据主')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-djxx-add">
            <h4 class="form-header h4">业务单据主信息</h4>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">单据编号：</label>
                    <div class="col-sm-8">
                        <input name="djbh" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">单据类型：</label>
                    <div class="col-sm-8">
                        <select name="djlx" class="form-control" th:with="type=${@dict.getType('swgx_yw_dj_type')}" required>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">单据状态：</label>
                    <div class="col-sm-8">
                        <select name="djzt" class="form-control" th:with="type=${@dict.getType('swgx_yw_dj_status')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">匹配状态：</label>
                    <div class="col-sm-8">
                        <select name="ppzt" class="form-control" th:with="type=${@dict.getType('swgx_yw_pp_status')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">单据日期：</label>
                    <div class="col-sm-8">
                        <div class="input-group date">
                            <input name="djrq" class="form-control" placeholder="yyyy-MM-dd HH:mm:ss" type="text" required>
                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">购方名称：</label>
                    <div class="col-sm-8">
                        <input name="gmfMc" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">购方税号：</label>
                    <div class="col-sm-8">
                        <input name="gmfNsrsbh" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">购方地址：</label>
                    <div class="col-sm-8">
                        <input name="gmfDz" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">购方电话：</label>
                    <div class="col-sm-8">
                        <input name="gmfDh" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">购方银行账号：</label>
                    <div class="col-sm-8">
                        <input name="gmfYhzh" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">合计金额：</label>
                    <div class="col-sm-8">
                        <input name="hjje" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">合计税额：</label>
                    <div class="col-sm-8">
                        <input name="hjse" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">价税合计：</label>
                    <div class="col-sm-8">
                        <input name="jshj" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">剩余合计金额：</label>
                    <div class="col-sm-8">
                        <input name="syHjje" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">剩余合计税额：</label>
                    <div class="col-sm-8">
                        <input name="syHjse" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">剩余价税合计：</label>
                    <div class="col-sm-8">
                        <input name="syJshj" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">红冲原因：</label>
                    <div class="col-sm-8">
                        <input name="hcYy" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">红冲次数：</label>
                    <div class="col-sm-8">
                        <input name="hcCs" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">业务系统单据ID：</label>
                    <div class="col-sm-8">
                        <input name="ywxtId" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">业务系统名称：</label>
                    <div class="col-sm-8">
                        <input name="ywxtMc" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注：</label>
                    <div class="col-sm-8">
                        <textarea name="bz" class="form-control"></textarea>
                    </div>
                </div>
            </div>
            <h4 class="form-header h4">业务单据明细信息</h4>
            <div class="row">
                <div class="col-xs-12">
                    <button type="button" class="btn btn-white btn-sm" onclick="addRow()"><i class="fa fa-plus"> 增加</i></button>
                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delRow()"><i class="fa fa-minus"> 删除</i></button>
                    <div class="col-sm-12 select-table table-striped">
                        <table id="bootstrap-table"></table>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "swgx/djxx"
        var ppZtDatas = [[${@dict.getType('swgx_yw_pp_status')}]];
        $("#form-djxx-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-djxx-add').serialize());
            }
        }

        $("input[name='djrq']").datetimepicker({
            format: "yyyy-mm-dd hh:ii:ss",
            minView: "hour",
            autoclose: true
        });

        $(function() {
            var options = {
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'xh',
                    align: 'center',
                    title: '序号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywDjmxList[%s].xh' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'spMc',
                    align: 'center',
                    title: '商品名称',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywDjmxList[%s].spMc' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'spBm',
                    align: 'center',
                    title: '商品编码',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywDjmxList[%s].spBm' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'ggxh',
                    align: 'center',
                    title: '规格型号',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywDjmxList[%s].ggxh' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'dw',
                    align: 'center',
                    title: '单位',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywDjmxList[%s].dw' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'sl',
                    align: 'center',
                    title: '数量',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywDjmxList[%s].sl' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'dj',
                    align: 'center',
                    title: '单价',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywDjmxList[%s].dj' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'je',
                    align: 'center',
                    title: '金额',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywDjmxList[%s].je' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'slv',
                    align: 'center',
                    title: '税率',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywDjmxList[%s].slv' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'se',
                    align: 'center',
                    title: '税额',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='ywDjmxList[%s].se' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var value = $.common.isNotEmpty(row.index) ? row.index : $.table.serialNumber(index);
                        return '<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="sub.delRowByIndex(\'' + value + '\')"><i class="fa fa-remove"></i>删除</a>';
                    }
                }]
            };
            $.table.init(options);
        });

        function addRow() {
            var count = $("#" + table.options.id).bootstrapTable('getData').length;
            var row = {
                xh: count + 1,
                spMc: "",
                spBm: "",
                ggxh: "",
                dw: "",
                sl: "",
                dj: "",
                je: "",
                slv: "",
                se: "",
            }
            sub.addRow(row);
        }
    </script>
</body>
</html>