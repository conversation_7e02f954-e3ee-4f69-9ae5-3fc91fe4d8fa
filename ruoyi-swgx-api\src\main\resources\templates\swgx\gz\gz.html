<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('匹配规则配置列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>规则名称 </label>
                                <input type="text" name="gzMc"/>
                            </li>
                            <li>
                                <label>规则类型 </label>
                                <select name="gzLx" th:with="type=${@dict.getType('swgx_match_rule_type')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>规则内容 </label>
                                <input type="text" name="gzNr"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="swgx:gz:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="swgx:gz:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="swgx:gz:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="swgx:gz:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('swgx:gz:edit')}]];
        var removeFlag = [[${@permission.hasPermi('swgx:gz:remove')}]];
        var gzLxDatas = [[${@dict.getType('swgx_match_rule_type')}]];
        var prefix = ctx + "swgx/gz";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "匹配规则配置",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '规则ID',
                    visible: false
                },
                {
                    field: 'gzMc',
                    title: '规则名称',
                    width: 150
                },
                {
                    field: 'gzMs',
                    title: '规则描述',
                    width: 200
                },
                {
                    field: 'gzLx',
                    title: '规则类型',
                    width: 100,
                    align: 'center',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(gzLxDatas, value);
                    }
                },
                {
                    field: 'gzZt',
                    title: '规则状态',
                    width: 80,
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '1') {
                            return '<span class="label label-success">启用</span>';
                        } else {
                            return '<span class="label label-danger">禁用</span>';
                        }
                    }
                },
                {
                    field: 'gzYxj',
                    title: '优先级',
                    width: 80,
                    align: 'center'
                },
                {
                    field: 'gzNr',
                    title: '规则内容',
                    formatter: function(value, row, index) {
                        if (value && value.length > 50) {
                            return '<span title="' + value + '">' + value.substring(0, 50) + '...</span>';
                        }
                        return value;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 120,
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>