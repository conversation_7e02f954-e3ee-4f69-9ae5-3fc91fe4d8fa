package com.ruoyi.swgx.service;

import java.util.List;
import com.ruoyi.swgx.domain.YwDjxx;

/**
 * 业务单据主Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IYwDjxxService 
{
    /**
     * 查询业务单据主
     * 
     * @param id 业务单据主主键
     * @return 业务单据主
     */
    public YwDjxx selectYwDjxxById(String id);

    /**
     * 查询业务单据主列表
     * 
     * @param ywDjxx 业务单据主
     * @return 业务单据主集合
     */
    public List<YwDjxx> selectYwDjxxList(YwDjxx ywDjxx);

    /**
     * 新增业务单据主
     * 
     * @param ywDjxx 业务单据主
     * @return 结果
     */
    public int insertYwDjxx(YwDjxx ywDjxx);

    /**
     * 修改业务单据主
     * 
     * @param ywDjxx 业务单据主
     * @return 结果
     */
    public int updateYwDjxx(YwDjxx ywDjxx);

    /**
     * 批量删除业务单据主
     * 
     * @param ids 需要删除的业务单据主主键集合
     * @return 结果
     */
    public int deleteYwDjxxByIds(String ids);

    /**
     * 删除业务单据主信息
     * 
     * @param id 业务单据主主键
     * @return 结果
     */
    public int deleteYwDjxxById(String id);
}
