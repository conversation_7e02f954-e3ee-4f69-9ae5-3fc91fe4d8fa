package com.ruoyi.swgx.mapper;

import com.ruoyi.swgx.domain.DpFpxx;
import com.ruoyi.swgx.domain.DpFpxxMx;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 电票发票信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface DpFpxxMapper {
    
    /**
     * 查询发票信息
     * 
     * @param id 发票信息主键
     * @return 发票信息
     */
    DpFpxx selectDpFpxxById(String id);

    /**
     * 根据发票代码和号码查询发票信息
     * 
     * @param fpDm 发票代码
     * @param fpHm 发票号码
     * @return 发票信息
     */
    DpFpxx selectDpFpxxByFpDmAndHm(@Param("fpDm") String fpDm, @Param("fpHm") String fpHm);

    /**
     * 查询发票信息列表
     * 
     * @param dpFpxx 发票信息
     * @return 发票信息集合
     */
    List<DpFpxx> selectDpFpxxList(DpFpxx dpFpxx);

    /**
     * 根据企业ID和时间范围查询发票信息
     * 
     * @param qyId 企业ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 发票信息集合
     */
    List<DpFpxx> selectDpFpxxByQyIdAndDateRange(@Param("qyId") String qyId, 
                                                @Param("startDate") String startDate, 
                                                @Param("endDate") String endDate);

    /**
     * 根据购买方纳税人识别号查询发票信息
     * 
     * @param gmfNsrsbh 购买方纳税人识别号
     * @return 发票信息集合
     */
    List<DpFpxx> selectDpFpxxByGmfNsrsbh(@Param("gmfNsrsbh") String gmfNsrsbh);

    /**
     * 统计发票数量按状态分组
     * 
     * @param qyId 企业ID
     * @return 统计结果
     */
    List<Map<String, Object>> countInvoiceByStatus(@Param("qyId") String qyId);

    /**
     * 统计发票金额汇总
     * 
     * @param qyId 企业ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 金额汇总统计
     */
    Map<String, Object> sumInvoiceAmount(@Param("qyId") String qyId, 
                                        @Param("startDate") String startDate, 
                                        @Param("endDate") String endDate);

    /**
     * 新增发票信息
     * 
     * @param dpFpxx 发票信息
     * @return 结果
     */
    int insertDpFpxx(DpFpxx dpFpxx);

    /**
     * 修改发票信息
     * 
     * @param dpFpxx 发票信息
     * @return 结果
     */
    int updateDpFpxx(DpFpxx dpFpxx);

    /**
     * 更新发票状态
     * 
     * @param id 发票ID
     * @param fpzt 发票状态
     * @param updateBy 更新人
     * @return 结果
     */
    int updateInvoiceStatus(@Param("id") String id, @Param("fpzt") Integer fpzt, @Param("updateBy") String updateBy);

    /**
     * 删除发票信息
     * 
     * @param id 发票信息主键
     * @return 结果
     */
    int deleteDpFpxxById(String id);

    /**
     * 批量删除发票信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteDpFpxxByIds(String[] ids);

    /**
     * 查询发票明细信息
     * 
     * @param fpid 发票ID
     * @return 发票明细集合
     */
    List<DpFpxxMx> selectDpFpxxMxByFpid(String fpid);

    /**
     * 批量新增发票明细
     * 
     * @param dpFpxxMxList 发票明细列表
     * @return 结果
     */
    int batchDpFpxxMx(List<DpFpxxMx> dpFpxxMxList);

    /**
     * 通过发票ID删除发票明细信息
     * 
     * @param fpid 发票ID
     * @return 结果
     */
    int deleteDpFpxxMxByFpid(String fpid);

    /**
     * 批量删除发票明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteDpFpxxMxByIds(String[] ids);

    /**
     * 新增发票明细信息
     * 
     * @param dpFpxxMx 发票明细信息
     * @return 结果
     */
    int insertDpFpxxMx(DpFpxxMx dpFpxxMx);

    /**
     * 修改发票明细信息
     * 
     * @param dpFpxxMx 发票明细信息
     * @return 结果
     */
    int updateDpFpxxMx(DpFpxxMx dpFpxxMx);

    /**
     * 删除发票明细信息
     * 
     * @param id 发票明细主键
     * @return 结果
     */
    int deleteDpFpxxMxById(String id);

    /**
     * 根据商品名称模糊查询发票明细
     * 
     * @param spmc 商品名称
     * @param qyId 企业ID
     * @return 发票明细集合
     */
    List<DpFpxxMx> selectDpFpxxMxBySpmc(@Param("spmc") String spmc, @Param("qyId") String qyId);

    /**
     * 统计发票明细金额汇总
     * 
     * @param fpid 发票ID
     * @return 金额汇总信息
     */
    Map<String, Object> sumInvoiceDetailAmount(@Param("fpid") String fpid);

    /**
     * 根据商品编码查询发票明细
     *
     * @param spbm 商品编码
     * @param qyId 企业ID
     * @return 发票明细集合
     */
    List<DpFpxxMx> selectDpFpxxMxBySpbm(@Param("spbm") String spbm, @Param("qyId") String qyId);

    /**
     * 查询未初始化匹配状态的电票明细
     *
     * @return 未初始化的电票明细集合
     */
    List<DpFpxxMx> selectUninitializedDetails();

    /**
     * 查询可用于匹配的电票明细
     *
     * @param gmfNsrsbh 购买方纳税人识别号
     * @return 可匹配的电票明细集合
     */
    List<DpFpxxMx> selectAvailableDetailsForMatch(@Param("gmfNsrsbh") String gmfNsrsbh);

    /**
     * 查询发票明细统计信息（按商品分组）
     *
     * @param qyId 企业ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 商品统计信息
     */
    List<Map<String, Object>> selectProductStatistics(@Param("qyId") String qyId,
                                                      @Param("startDate") String startDate,
                                                      @Param("endDate") String endDate);

    /**
     * 统计所有电票明细数量
     *
     * @return 电票明细总数
     */
    int countAllDetails();

    /**
     * 查询可红冲的发票信息列表
     *
     * @param dpFpxx 查询条件
     * @return 可红冲的发票信息集合
     */
    List<DpFpxx> selectRedFlushableInvoiceList(DpFpxx dpFpxx);
}
