package com.ruoyi.swgx.service;

import com.ruoyi.swgx.domain.DpFpxx;
import com.ruoyi.swgx.domain.DpFpxxMx;

import java.util.List;
import java.util.Map;

/**
 * 电票发票信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface IDpFpxxService {
    
    /**
     * 查询发票信息
     * 
     * @param id 发票信息主键
     * @return 发票信息
     */
    DpFpxx selectDpFpxxById(String id);

    /**
     * 根据发票代码和号码查询发票信息
     * 
     * @param fpDm 发票代码
     * @param fpHm 发票号码
     * @return 发票信息
     */
    DpFpxx selectDpFpxxByFpDmAndHm(String fpDm, String fpHm);

    /**
     * 查询发票信息列表
     * 
     * @param dpFpxx 发票信息
     * @return 发票信息集合
     */
    List<DpFpxx> selectDpFpxxList(DpFpxx dpFpxx);

    /**
     * 根据企业ID和时间范围查询发票信息
     * 
     * @param qyId 企业ID
     * @param startDate 开始日期(yyyyMMdd)
     * @param endDate 结束日期(yyyyMMdd)
     * @return 发票信息集合
     */
    List<DpFpxx> selectDpFpxxByQyIdAndDateRange(String qyId, String startDate, String endDate);

    /**
     * 根据购买方纳税人识别号查询发票信息
     * 
     * @param gmfNsrsbh 购买方纳税人识别号
     * @return 发票信息集合
     */
    List<DpFpxx> selectDpFpxxByGmfNsrsbh(String gmfNsrsbh);

    /**
     * 统计发票数量按状态分组
     * 
     * @param qyId 企业ID
     * @return 统计结果
     */
    List<Map<String, Object>> countInvoiceByStatus(String qyId);

    /**
     * 统计发票金额汇总
     * 
     * @param qyId 企业ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 金额汇总统计
     */
    Map<String, Object> sumInvoiceAmount(String qyId, String startDate, String endDate);

    /**
     * 新增发票信息
     * 
     * @param dpFpxx 发票信息
     * @return 结果
     */
    int insertDpFpxx(DpFpxx dpFpxx);

    /**
     * 修改发票信息
     * 
     * @param dpFpxx 发票信息
     * @return 结果
     */
    int updateDpFpxx(DpFpxx dpFpxx);

    /**
     * 更新发票状态
     * 
     * @param id 发票ID
     * @param fpzt 发票状态
     * @param updateBy 更新人
     * @return 结果
     */
    int updateInvoiceStatus(String id, Integer fpzt, String updateBy);

    /**
     * 批量删除发票信息
     * 
     * @param ids 需要删除的发票信息主键集合
     * @return 结果
     */
    int deleteDpFpxxByIds(String[] ids);

    /**
     * 删除发票信息
     * 
     * @param id 发票信息主键
     * @return 结果
     */
    int deleteDpFpxxById(String id);

    /**
     * 查询发票明细信息
     * 
     * @param fpid 发票ID
     * @return 发票明细集合
     */
    List<DpFpxxMx> selectDpFpxxMxByFpid(String fpid);

    /**
     * 根据商品名称模糊查询发票明细
     * 
     * @param spmc 商品名称
     * @param qyId 企业ID
     * @return 发票明细集合
     */
    List<DpFpxxMx> selectDpFpxxMxBySpmc(String spmc, String qyId);

    /**
     * 根据商品编码查询发票明细
     * 
     * @param spbm 商品编码
     * @param qyId 企业ID
     * @return 发票明细集合
     */
    List<DpFpxxMx> selectDpFpxxMxBySpbm(String spbm, String qyId);

    /**
     * 统计发票明细金额汇总
     * 
     * @param fpid 发票ID
     * @return 金额汇总信息
     */
    Map<String, Object> sumInvoiceDetailAmount(String fpid);

    /**
     * 查询发票明细统计信息（按商品分组）
     * 
     * @param qyId 企业ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 商品统计信息
     */
    List<Map<String, Object>> selectProductStatistics(String qyId, String startDate, String endDate);

    /**
     * 验证发票信息的完整性和合法性
     * 
     * @param dpFpxx 发票信息
     * @return 验证结果消息，null表示验证通过
     */
    String validateInvoiceData(DpFpxx dpFpxx);

    /**
     * 检查发票是否已存在
     * 
     * @param fpDm 发票代码
     * @param fpHm 发票号码
     * @param excludeId 排除的发票ID（用于修改时排除自身）
     * @return true-已存在，false-不存在
     */
    boolean checkInvoiceExists(String fpDm, String fpHm, String excludeId);

    /**
     * 批量导入发票信息
     * 
     * @param invoiceList 发票信息列表
     * @param updateSupport 是否支持更新已存在的发票
     * @return 导入结果统计
     */
    Map<String, Object> batchImportInvoices(List<DpFpxx> invoiceList, boolean updateSupport);

    /**
     * 导出发票信息
     *
     * @param dpFpxx 查询条件
     * @return 发票信息列表
     */
    List<DpFpxx> exportInvoiceList(DpFpxx dpFpxx);

    /**
     * 获取发票的红冲匹配信息
     *
     * @param dpFpxx 查询条件
     * @return 匹配的发票信息
     */
    List<DpFpxx> getRedFlushMatchingInvoices(DpFpxx dpFpxx);

    /**
     * 执行发票红冲操作
     *
     * @param originalInvoiceId 原发票ID
     * @param redFlushInvoice 红冲发票信息
     * @return 操作结果
     */
    boolean executeRedFlush(String originalInvoiceId, DpFpxx redFlushInvoice);
}
