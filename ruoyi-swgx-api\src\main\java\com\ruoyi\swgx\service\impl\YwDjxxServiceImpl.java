package com.ruoyi.swgx.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.swgx.domain.YwDjmx;
import com.ruoyi.swgx.mapper.YwDjxxMapper;
import com.ruoyi.swgx.domain.YwDjxx;
import com.ruoyi.swgx.service.IYwDjxxService;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.uuid.IdUtils;

/**
 * 业务单据主Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class YwDjxxServiceImpl implements IYwDjxxService 
{
    @Autowired
    private YwDjxxMapper ywDjxxMapper;

    /**
     * 查询业务单据主
     * 
     * @param id 业务单据主主键
     * @return 业务单据主
     */
    @Override
    public YwDjxx selectYwDjxxById(String id)
    {
        return ywDjxxMapper.selectYwDjxxById(id);
    }

    /**
     * 查询业务单据主列表
     * 
     * @param ywDjxx 业务单据主
     * @return 业务单据主
     */
    @Override
    public List<YwDjxx> selectYwDjxxList(YwDjxx ywDjxx)
    {
        return ywDjxxMapper.selectYwDjxxList(ywDjxx);
    }

    /**
     * 新增业务单据主
     * 
     * @param ywDjxx 业务单据主
     * @return 结果
     */
    @Transactional
    @Override
    public int insertYwDjxx(YwDjxx ywDjxx)
    {
        // 为主表记录生成UUID
        ywDjxx.setId(IdUtils.fastSimpleUUID());
        ywDjxx.setCreateTime(DateUtils.getNowDate());
        int rows = ywDjxxMapper.insertYwDjxx(ywDjxx);
        insertYwDjmx(ywDjxx);
        return rows;
    }

    /**
     * 修改业务单据主
     * 
     * @param ywDjxx 业务单据主
     * @return 结果
     */
    @Transactional
    @Override
    public int updateYwDjxx(YwDjxx ywDjxx)
    {
        ywDjxx.setUpdateTime(DateUtils.getNowDate());
        ywDjxxMapper.deleteYwDjmxByDjId(ywDjxx.getId());
        insertYwDjmx(ywDjxx);
        return ywDjxxMapper.updateYwDjxx(ywDjxx);
    }

    /**
     * 批量删除业务单据主
     * 
     * @param ids 需要删除的业务单据主主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteYwDjxxByIds(String ids)
    {
        ywDjxxMapper.deleteYwDjmxByDjIds(Convert.toStrArray(ids));
        return ywDjxxMapper.deleteYwDjxxByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除业务单据主信息
     * 
     * @param id 业务单据主主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteYwDjxxById(String id)
    {
        ywDjxxMapper.deleteYwDjmxByDjId(id);
        return ywDjxxMapper.deleteYwDjxxById(id);
    }

    /**
     * 新增业务单据明细信息
     * 
     * @param ywDjxx 业务单据主对象
     */
    public void insertYwDjmx(YwDjxx ywDjxx)
    {
        List<YwDjmx> ywDjmxList = ywDjxx.getYwDjmxList();
        String id = ywDjxx.getId();
        if (StringUtils.isNotNull(ywDjmxList))
        {
            List<YwDjmx> list = new ArrayList<YwDjmx>();
            for (YwDjmx ywDjmx : ywDjmxList)
            {
                // 为明细记录生成UUID
                ywDjmx.setId(IdUtils.fastSimpleUUID());
                ywDjmx.setDjId(id);
                ywDjmx.setCreateTime(DateUtils.getNowDate());
                list.add(ywDjmx);
            }
            if (list.size() > 0)
            {
                ywDjxxMapper.batchYwDjmx(list);
            }
        }
    }
}
