<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('发票详情')" />
    <style type="text/css">
        .detail-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .detail-info .form-group {
            margin-bottom: 10px;
        }
        .detail-info label {
            font-weight: bold;
            color: #333;
        }
        .detail-table {
            margin-top: 20px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        .status-normal { background-color: #d4edda; color: #155724; }
        .status-cancelled { background-color: #f8d7da; color: #721c24; }
        .status-red-flushed { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body class="gray-bg">
    <div class="main-content">
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>发票基本信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="detail-info">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>发票代码：</label>
                                        <span id="fpDm"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>发票号码：</label>
                                        <span id="fpHm"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>发票状态：</label>
                                        <span id="fpzt"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>开票日期：</label>
                                        <span id="kprq"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>销售方名称：</label>
                                        <span id="xsfMc"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>销售方纳税人识别号：</label>
                                        <span id="xsfNsrsbh"></span>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>购买方名称：</label>
                                        <span id="gmfMc"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>购买方纳税人识别号：</label>
                                        <span id="gmfNsrsbh"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>合计金额：</label>
                                        <span id="hjje"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>合计税额：</label>
                                        <span id="hjse"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>价税合计：</label>
                                        <span id="jshj"></span>
                                    </div>
                                    <div class="form-group">
                                        <label>开票人：</label>
                                        <span id="kpr"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label>备注：</label>
                                        <span id="bz"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>发票明细信息</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="detail-table">
                            <table id="bootstrap-table" class="table table-striped"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 隐藏的发票ID -->
    <input type="hidden" id="hiddenInvoiceId" th:value="${id}" />

    <th:block th:include="include :: footer" />
    <script th:src="@{/js/swgx-common.js}"></script>
    <script th:inline="javascript">
        var invoiceId = $("#hiddenInvoiceId").val();
        var prefix = ctx + "swgx/dpFpxx";

        $(function() {
            // 重新获取发票ID，确保DOM已加载
            invoiceId = $("#hiddenInvoiceId").val();


            if (!invoiceId) {
                $.modal.alertError("发票ID为空，无法加载详情");
                return;
            }

            // 通过API加载发票信息（包含明细）
            loadInvoiceDetail();
        });

        function displayInvoiceDetail(data) {
            if (!data) {
                $.modal.alertError("发票数据为空");
                return;
            }

            $("#fpDm").text(data.fpDm || '-');
            $("#fpHm").text(data.fpHm || '-');
            $("#fpzt").html(getStatusBadge(data.fpzt));
            $("#kprq").text(formatDate(data.kprq) || '-');
            $("#xsfMc").text(data.xsfMc || '-');
            $("#xsfNsrsbh").text(data.xsfNsrsbh || '-');
            $("#gmfMc").text(data.gmfMc || '-');
            $("#gmfNsrsbh").text(data.gmfNsrsbh || '-');
            $("#hjje").text(data.hjje ? '¥' + data.hjje : '-');
            $("#hjse").text(data.hjse ? '¥' + data.hjse : '-');
            $("#jshj").text(data.jshj ? '¥' + data.jshj : '-');
            $("#kpr").text(data.kpr || '-');
            $("#bz").text(data.bz || '-');
        }

        function loadInvoiceDetail() {
            $.get(prefix + "/getInfo/" + invoiceId, function(result) {
                if (result.code == web_status.SUCCESS && result.data) {
                    displayInvoiceDetail(result.data);

                    // 如果API返回的数据中包含明细信息，直接使用
                    if (result.data.dpFpxxMxList && result.data.dpFpxxMxList.length > 0) {
                        displayDetailTable(result.data.dpFpxxMxList);
                    } else {
                        // 如果没有明细数据，通过明细API获取
                        loadDetailData();
                    }
                } else {
                    var errorMsg = result.msg || "未知错误";
                    if (!result.data) {
                        errorMsg = "发票数据为空，发票ID：" + invoiceId;
                    }
                    $.modal.alertError("加载发票信息失败：" + errorMsg);
                }
            }).fail(function(xhr, status, error) {
                $.modal.alertError("请求失败：" + error);
            });
        }

        function displayDetailTable(detailData) {
            var options = {
                data: detailData,
                pagination: false,
                search: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                columns: [{
                    field: 'serialNumber',
                    title: '序号',
                    align: 'center',
                    width: 60
                }, {
                    field: 'spmc',
                    title: '商品名称',
                    align: 'left'
                }, {
                    field: 'ggxh',
                    title: '规格型号',
                    align: 'center'
                }, {
                    field: 'dw',
                    title: '单位',
                    align: 'center',
                    width: 80
                }, {
                    field: 'spsl',
                    title: '数量',
                    align: 'right',
                    width: 120,
                    formatter: function(value) {
                        // 使用统一的数量格式化器，显示12位小数
                        return value ? SwgxCommon.formatQuantity(value) : '-';
                    }
                }, {
                    field: 'dj',
                    title: '单价',
                    align: 'right',
                    width: 120,
                    formatter: function(value) {
                        // 使用统一的单价格式化器，显示12位小数
                        return value ? SwgxCommon.formatPrice(value, true) : '-';
                    }
                }, {
                    field: 'je',
                    title: '金额',
                    align: 'right',
                    width: 120,
                    formatter: function(value) {
                        return value ? '¥' + parseFloat(value).toFixed(2) : '-';
                    }
                }, {
                    field: 'sl',
                    title: '税率',
                    align: 'right',
                    width: 100,
                    formatter: function(value) {
                        // 使用统一的税率格式化器，显示为百分比
                        return value ? SwgxCommon.formatTaxRate(value, true) : '-';
                    }
                }, {
                    field: 'se',
                    title: '税额',
                    align: 'right',
                    width: 120,
                    formatter: function(value) {
                        // 使用统一的税额格式化器
                        return value ? SwgxCommon.formatTaxAmount(value, true) : '-';
                    }
                }]
            };
            $("#bootstrap-table").bootstrapTable('destroy').bootstrapTable(options);
        }

        function loadDetailData() {
            $.get(prefix + "/detailList/" + invoiceId, function(result) {
                if (result.code == web_status.SUCCESS && result.data) {
                    displayDetailTable(result.data);
                }
            });
        }


        
        function getStatusBadge(status) {
            var statusMap = {
                0: '<span class="status-badge status-normal">正常</span>',
                1: '<span class="status-badge status-cancelled">作废</span>',
                2: '<span class="status-badge status-red-flushed">红冲</span>'
            };
            return statusMap[status] || '<span class="status-badge">未知</span>';
        }
        
        function formatDate(dateStr) {
            if (!dateStr || dateStr.length < 8) return dateStr;
            // yyyyMMddHHmmss -> yyyy-MM-dd HH:mm:ss
            var year = dateStr.substring(0, 4);
            var month = dateStr.substring(4, 6);
            var day = dateStr.substring(6, 8);
            var hour = dateStr.substring(8, 10) || '00';
            var minute = dateStr.substring(10, 12) || '00';
            var second = dateStr.substring(12, 14) || '00';
            return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
        }
    </script>
</body>
</html>
