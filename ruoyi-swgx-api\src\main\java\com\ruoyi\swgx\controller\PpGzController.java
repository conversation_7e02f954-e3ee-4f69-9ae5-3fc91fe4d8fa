package com.ruoyi.swgx.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.swgx.domain.PpGz;
import com.ruoyi.swgx.service.IPpGzService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 匹配规则配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Controller
@RequestMapping("/swgx/gz")
public class PpGzController extends BaseController
{
    private String prefix = "swgx/gz";

    @Autowired
    private IPpGzService ppGzService;

    @RequiresPermissions("swgx:gz:view")
    @GetMapping()
    public String gz()
    {
        return prefix + "/gz";
    }

    /**
     * 查询匹配规则配置列表
     */
    @RequiresPermissions("swgx:gz:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(PpGz ppGz)
    {
        startPage();
        List<PpGz> list = ppGzService.selectPpGzList(ppGz);
        return getDataTable(list);
    }

    /**
     * 导出匹配规则配置列表
     */
    @RequiresPermissions("swgx:gz:export")
    @Log(title = "匹配规则配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(PpGz ppGz)
    {
        List<PpGz> list = ppGzService.selectPpGzList(ppGz);
        ExcelUtil<PpGz> util = new ExcelUtil<PpGz>(PpGz.class);
        return util.exportExcel(list, "匹配规则配置数据");
    }

    /**
     * 新增匹配规则配置
     */
    @RequiresPermissions("swgx:gz:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存匹配规则配置
     */
    @RequiresPermissions("swgx:gz:add")
    @Log(title = "匹配规则配置", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(PpGz ppGz)
    {
        return toAjax(ppGzService.insertPpGz(ppGz));
    }

    /**
     * 修改匹配规则配置
     */
    @RequiresPermissions("swgx:gz:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap)
    {
        PpGz ppGz = ppGzService.selectPpGzById(id);
        mmap.put("ppGz", ppGz);
        return prefix + "/edit";
    }

    /**
     * 修改保存匹配规则配置
     */
    @RequiresPermissions("swgx:gz:edit")
    @Log(title = "匹配规则配置", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(PpGz ppGz)
    {
        return toAjax(ppGzService.updatePpGz(ppGz));
    }

    /**
     * 删除匹配规则配置
     */
    @RequiresPermissions("swgx:gz:remove")
    @Log(title = "匹配规则配置", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(ppGzService.deletePpGzByIds(ids));
    }
}
